#!/usr/bin/env python3
"""
CLEAN MARKET DATA AGENT
Modern implementation with proper async handling and date formats

Features:
- Proper SmartAPI date format handling (YYYY-MM-DD HH:MM)
- Modern timeframes: 1min, 2min, 3min, 5min, 10min, 15min, 30min, 1hr
- Clean async/await patterns
- WebSocket v2 integration
- Comprehensive error handling
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import polars as pl
import random
import os
import json
from pathlib import Path
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from core.base_agent import BaseAgent
from core.event_system import EventBus, EventTypes
from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials, HistoricalDataRequest

logger = logging.getLogger(__name__)

class CleanMarketDataAgent(BaseAgent):
    """
    Clean Market Data Agent with modern architecture
    
    Features:
    - Proper date format handling
    - Modern timeframes
    - Clean async patterns
    - WebSocket v2 integration
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("CleanMarketDataAgent", event_bus, config, session_id)
        
        # SmartAPI client
        self.smartapi_client = None
        
        # Data storage with training timeframes
        training_timeframes = getattr(config, 'timeframes', ["1min", "3min", "5min", "15min"])
        self.timeframe_data = {tf: {} for tf in training_timeframes}
        
        # Real-time data
        self.realtime_data = {}
        
        # Instrument mapping
        self.instrument_map = {}
        self.symbol_to_token_map = self._load_token_mapping()

        # Configuration - will be updated dynamically by stock selector
        self.config = config
        self.selected_stocks = []  # Start empty, will be updated by stock selector
        
        # Retry configuration for historical data downloads
        self.retry_config = {
            'max_retries': getattr(config, 'data_download_max_retries', 3),
            'base_delay': getattr(config, 'data_download_base_delay', 2.0),  # seconds
            'max_delay': getattr(config, 'data_download_max_delay', 30.0),   # seconds
            'exponential_base': getattr(config, 'data_download_exponential_base', 2.0),
            'jitter': getattr(config, 'data_download_jitter', True)
        }
        
        # Download workers for concurrent data fetching
        self.data_cache: Dict[str, pl.DataFrame] = {}
        self.download_queue = asyncio.Queue()
        self.num_download_workers = 10
        self.download_workers: List[asyncio.Task] = []
        
        self.log_info(f"Initialized with {self.num_download_workers} download workers.")

    def _load_token_mapping(self) -> Dict[str, str]:
        try:
            mapping_file_path = Path(__file__).parent.parent / 'token_mapping.json'
            with open(mapping_file_path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Successfully loaded token mapping from {mapping_file_path}")
                return data.get("symbol_to_token", {})
        except Exception as e:
            self.log_error(f"Could not load or parse token_mapping.json: {e}")
            return {}

    async def initialize(self) -> bool:
        self.log_info("Initializing Clean Market Data Agent...")
        credentials = self._load_credentials()
        if credentials:
            self.smartapi_client = ModernSmartAPIClient(credentials)
            if not await self.smartapi_client.authenticate():
                self.log_error("SmartAPI authentication failed, will operate in demo mode.")
                self.smartapi_client = None
        else:
            self.log_warning("No SmartAPI credentials, will operate in demo mode.")

        self.event_bus.subscribe("REQUEST_HISTORICAL_DATA", self._handle_data_request)
        self.initialized = True
        self.log_info("Clean Market Data Agent initialized.")
        return True

    def _load_credentials(self) -> Optional[SmartAPICredentials]:
        try:
            api_key, username, password, totp_token = (
                os.getenv('SMARTAPI_API_KEY'), os.getenv('SMARTAPI_USERNAME'),
                os.getenv('SMARTAPI_PASSWORD'), os.getenv('SMARTAPI_TOTP_TOKEN')
            )
            if all((api_key, username, password, totp_token)):
                return SmartAPICredentials(api_key, username, password, totp_token)
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
        return None

    async def start(self):
        self.log_info("Starting download workers...")
        self.running = True
        self.download_workers = [
            asyncio.create_task(self._download_worker(i)) for i in range(self.num_download_workers)
        ]

    async def _download_worker(self, worker_id: int):
        self.log_info(f"Download worker {worker_id} started.")
        while self.running:
            try:
                symbol, timeframe, event_to_set = await self.download_queue.get()
                self.log_info(f"Worker {worker_id} processing request for {symbol} ({timeframe})")
                
                data = None
                try:
                    data = await self._fetch_and_process_data(symbol, timeframe)
                except Exception as e:
                    self.log_error(f"Worker {worker_id} failed to process {symbol}: {e}")
                
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {'symbol': symbol, 'timeframe': timeframe, 'data': data},
                    source=self.name
                )
                if event_to_set:
                    event_to_set.set()
                self.download_queue.task_done()
            except asyncio.CancelledError:
                break
        self.log_info(f"Download worker {worker_id} stopped.")

    async def _fetch_and_process_data(self, symbol: str, timeframe: str) -> Optional[pl.DataFrame]:
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.data_cache:
            self.log_info(f"Cache hit for {cache_key}.")
            return self.data_cache[cache_key]

        days = 90 if timeframe == '1day' else 30
        interval_map = {'1day': 'ONE_DAY', '1min': 'ONE_MINUTE'}
        api_interval = interval_map.get(timeframe)
        if not api_interval:
            self.log_warning(f"Unsupported timeframe '{timeframe}' for {symbol}")
            return None

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        try:
            instrument = self._get_instrument_details(symbol)
            if not instrument or instrument.get('demo'):
                raise ValueError("No real token found.")

            df = await self._retry_with_backoff(
                self._download_real_data, symbol, instrument, start_date, end_date, api_interval
            )
            self.data_cache[cache_key] = df
            return df
        except Exception as e:
            self.log_warning(f"All attempts to fetch real data for {symbol} failed: {e}. Falling back to demo data.")
            df = self._create_demo_data(symbol, start_date, end_date, timeframe)
            self.data_cache[cache_key] = df
            return df

    async def _download_real_data(self, symbol: str, instrument: Dict, start_date: datetime, end_date: datetime, interval: str) -> pl.DataFrame:
        if not self.smartapi_client:
            raise ConnectionError("SmartAPI client not authenticated.")

        data = await self.smartapi_client.get_historical_data_batch(
            symbol_token=instrument['token'], exchange=instrument['exchange'],
            start_date=start_date, end_date=end_date, interval=interval
        )
        if not data:
            raise ValueError(f"API returned no data for {symbol}")

        df = pl.DataFrame(data, schema=[
            ("timestamp", pl.Utf8), ("open", pl.Float64), ("high", pl.Float64),
            ("low", pl.Float64), ("close", pl.Float64), ("volume", pl.Int64)
        ]).with_columns(
            pl.col("timestamp").str.to_datetime("%Y-%m-%dT%H:%M:%S%z")
        ).sort("timestamp")
        
        if df.is_empty() or df.height < 20:
            raise ValueError(f"Insufficient data for {symbol} ({df.height} rows)")
        
        self.log_info(f"Successfully downloaded {df.height} rows for {symbol}")
        return df

    def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime, timeframe: str) -> pl.DataFrame:
        self.log_info(f"Creating demo data for {symbol}")
        interval = "1d" if timeframe == '1day' else "1m"
        dates = pl.date_range(start_date, end_date, interval, eager=True).alias("timestamp")
        
        base_price = random.uniform(100, 3000)
        price_noise = np.random.normal(loc=0, scale=base_price * 0.02, size=len(dates))
        close_prices = base_price + np.cumsum(price_noise)
        
        df = pl.DataFrame({
            "timestamp": dates,
            "open": close_prices - np.random.uniform(0, 5, len(dates)),
            "high": close_prices + np.random.uniform(0, 5, len(dates)),
            "low": close_prices - np.random.uniform(0, 5, len(dates)),
            "close": close_prices,
            "volume": np.random.randint(100_000, 5_000_000, size=len(dates))
        })
        return df

    def _get_instrument_details(self, symbol: str) -> Optional[Dict]:
        keys_to_check = [f"{symbol}-EQ_NSE", f"{symbol}_NSE", symbol]
        token = next((self.symbol_to_token_map[key] for key in keys_to_check if key in self.symbol_to_token_map), None)
        
        if token:
            return {'token': token, 'symbol': symbol, 'exchange': 'NSE'}
        return {'token': f"DEMO_{symbol}", 'symbol': symbol, 'exchange': 'NSE', 'demo': True}

    async def _handle_data_request(self, event):
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1day')
            event_to_set = event.data.get('event_to_set')
            if symbol:
                await self.download_queue.put((symbol, timeframe, event_to_set))
        except Exception as e:
            self.log_error(f"Failed to queue data request: {e}")

    async def _handle_universe_update(self, event):
        self.log_info("Noted updated universe. Data will be fetched on-demand.")

    async def _retry_with_backoff(self, func, *args, **kwargs):
        last_exception = None
        for attempt in range(self.retry_config['max_retries']):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                delay = self.retry_config['base_delay'] * (self.retry_config['exponential_base'] ** attempt)
                if self.retry_config['jitter']:
                    delay *= random.uniform(0.5, 1.5)
                delay = min(delay, self.retry_config['max_delay'])
                self.log_warning(f"Attempt {attempt + 1} for {args[0]} failed. Retrying in {delay:.2f}s...")
                await asyncio.sleep(delay)
        raise last_exception

    async def stop(self):
        self.log_info("Stopping Clean Market Data Agent...")
        self.running = False
        for worker in self.download_workers:
            worker.cancel()
        await asyncio.gather(*self.download_workers, return_exceptions=True)
        self.log_info("Clean Market Data Agent stopped.")

    def _load_token_mapping(self) -> Dict[str, str]:
        """Load the symbol to token mapping from the JSON file."""
        try:
            # Path to token_mapping.json relative to this file
            mapping_file_path = Path(__file__).parent.parent / 'token_mapping.json'
            with open(mapping_file_path, 'r') as f:
                data = json.load(f)
                self.log_info(f"Successfully loaded token mapping from {mapping_file_path}")
                return data.get("symbol_to_token", {})
        except FileNotFoundError:
            self.log_error(f"token_mapping.json not found at {mapping_file_path}")
            return {}
        except json.JSONDecodeError:
            self.log_error(f"Error decoding JSON from token_mapping.json.")
            return {}
        except Exception as e:
            self.log_error(f"An error occurred while loading token mapping: {e}")
            return {}

    async def initialize(self) -> bool:
        """Initialize the market data agent"""
        try:
            self.log_info("Initializing Clean Market Data Agent...")
            
            # Load credentials
            credentials = self._load_credentials()
            if not credentials:
                self.log_warning("No SmartAPI credentials found, running in demo mode")
                self.initialized = True
                return True
            
            # Initialize SmartAPI client
            self.smartapi_client = ModernSmartAPIClient(credentials)
            
            # Authenticate
            auth_success = await self.smartapi_client.authenticate()
            if not auth_success:
                self.log_error("SmartAPI authentication failed, falling back to demo mode")
                self.smartapi_client = None
            
            # Subscribe to stock universe updates
            self.event_bus.subscribe("STOCK_UNIVERSE_UPDATED", self._handle_universe_update)
            
            # Initialize instrument mapping
            await self._initialize_instruments()
            
            self.initialized = True
            self.log_info("Clean Market Data Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize: {e}")
            return False
    
    def _load_credentials(self) -> Optional[SmartAPICredentials]:
        """Load SmartAPI credentials from environment"""
        try:
            api_key = os.getenv('SMARTAPI_API_KEY')
            username = os.getenv('SMARTAPI_USERNAME')
            password = os.getenv('SMARTAPI_PASSWORD')
            totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
            
            self.log_info(f"Loading credentials - API Key: {'***' if api_key else 'None'}, Username: {'***' if username else 'None'}")
            
            if all([api_key, username, password, totp_token]):
                self.log_info("All SmartAPI credentials found")
                return SmartAPICredentials(
                    api_key=api_key,
                    username=username,
                    password=password,
                    totp_token=totp_token
                )
            else:
                missing = []
                if not api_key: missing.append('SMARTAPI_API_KEY')
                if not username: missing.append('SMARTAPI_USERNAME')
                if not password: missing.append('SMARTAPI_PASSWORD')
                if not totp_token: missing.append('SMARTAPI_TOTP_TOKEN')
                self.log_warning(f"Missing credentials: {', '.join(missing)}")
            
            return None
            
        except Exception as e:
            self.log_error(f"Failed to load credentials: {e}")
            return None
    
    async def _initialize_instruments(self):
        """Initialize instrument mapping using the loaded token mapping file."""
        try:
            self.instrument_map = {} # Reset the map
            for symbol in self.selected_stocks:
                # Construct potential keys to check in the mapping file
                keys_to_check = [
                    f"{symbol}-EQ_NSE",
                    f"{symbol}_NSE",
                    symbol
                ]
                
                token = None
                for key in keys_to_check:
                    if key in self.symbol_to_token_map:
                        token = self.symbol_to_token_map[key]
                        break
                
                if token:
                    self.instrument_map[symbol] = {
                        'token': token,
                        'symbol': symbol,
                        'exchange': 'NSE',
                        'lot_size': 1
                    }
                else:
                    # Fallback for unknown symbols - use demo mode
                    self.log_warning(f"No token found for {symbol} in token_mapping.json, using demo data")
                    self.instrument_map[symbol] = {
                        'token': f"DEMO_{symbol}",
                        'symbol': symbol,
                        'exchange': 'NSE',
                        'lot_size': 1,
                        'demo': True
                    }

            self.log_info(f"Initialized instruments for {len(self.instrument_map)} symbols")
            real_count = sum(1 for inst in self.instrument_map.values() if not inst.get('demo', False))
            demo_count = len(self.instrument_map) - real_count
            self.log_info(f"Real tokens: {real_count}, Demo tokens: {demo_count}")

        except Exception as e:
            self.log_error(f"Failed to initialize instruments: {e}")
    
    async def _handle_universe_update(self, event):
        """Handle stock universe update from dynamic selector"""
        try:
            new_universe = event.data.get('universe', [])
            if new_universe:
                old_count = len(self.selected_stocks)
                self.selected_stocks = new_universe

                self.log_info(f"Updated stock universe: {old_count} -> {len(self.selected_stocks)} stocks")
                self.log_info(f"New universe: {', '.join(new_universe[:10])}{'...' if len(new_universe) > 10 else ''}")

                # Re-initialize instruments for new stocks
                await self._initialize_instruments()

                # If we're already running, download data for new stocks
                if hasattr(self, 'running') and self.running:
                    await self._download_historical_data()

        except Exception as e:
            self.log_error(f"Failed to handle universe update: {e}")
    
    async def start(self):
        """Start the market data agent"""
        try:
            self.log_info("Starting Clean Market Data Agent...")
            
            # Wait for stock universe to be populated
            await self._wait_for_stock_universe()
            
            # Download historical data
            await self._download_historical_data()
            
            # Initialize WebSocket if available
            if self.smartapi_client:
                await self._initialize_websocket()
            
            # Start data processing loop
            await self._start_data_loop()
            
        except Exception as e:
            self.log_error(f"Error starting agent: {e}")
    
    async def _wait_for_stock_universe(self):
        """Wait for stock universe to be populated by dynamic selector"""
        try:
            self.log_info("Waiting for stock universe from dynamic selector...")

            # Wait up to 10 seconds for stock universe (reduced timeout)
            timeout = 10
            elapsed = 0

            while elapsed < timeout:
                if self.selected_stocks:
                    self.log_info(f"Received stock universe: {len(self.selected_stocks)} stocks")
                    return

                await asyncio.sleep(0.5)  # Check more frequently
                elapsed += 0.5

            # Check if we have a universe from the config that was updated
            if hasattr(self.config, 'selected_stocks') and self.config.selected_stocks:
                self.selected_stocks = self.config.selected_stocks
                self.log_info(f"Using universe from config: {len(self.selected_stocks)} stocks")
                await self._initialize_instruments()
                return

            # Timeout - use fallback stocks from config
            fallback_stocks = getattr(self.config, 'fallback_stocks', [
                "RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"
            ])

            self.log_warning(f"Stock universe timeout, using {len(fallback_stocks)} fallback stocks")
            self.selected_stocks = fallback_stocks
            await self._initialize_instruments()

        except Exception as e:
            self.log_error(f"Failed to wait for stock universe: {e}")
    
    async def _retry_with_backoff(self, func, *args, **kwargs):
        """
        Retry a function with exponential backoff and jitter
        
        Args:
            func: The async function to retry
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of the function call
            
        Raises:
            The last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.retry_config['max_retries'] + 1):  # +1 for initial attempt
            try:
                if attempt > 0:
                    # Calculate delay with exponential backoff
                    delay = min(
                        self.retry_config['base_delay'] * (self.retry_config['exponential_base'] ** (attempt - 1)),
                        self.retry_config['max_delay']
                    )
                    
                    # Add jitter to prevent thundering herd
                    if self.retry_config['jitter']:
                        delay = delay * (0.5 + random.random() * 0.5)  # 50-100% of calculated delay
                    
                    self.log_info(f"Retrying in {delay:.2f} seconds (attempt {attempt + 1}/{self.retry_config['max_retries'] + 1})")
                    await asyncio.sleep(delay)
                
                # Attempt the function call
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    self.log_info(f"Retry successful after {attempt} attempts")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.retry_config['max_retries']:
                    self.log_warning(f"Attempt {attempt + 1} failed: {e}")
                else:
                    self.log_error(f"All {self.retry_config['max_retries'] + 1} attempts failed. Last error: {e}")
        
        # If we get here, all retries failed
        raise last_exception
    
    async def _download_historical_data(self):
        """Download historical data with proper date handling and a global retry mechanism for failed stocks."""
        try:
            self.log_info(f"Starting historical data download for {len(self.selected_stocks)} stocks...")
            
            end_date = datetime.now().replace(hour=15, minute=30, second=0, microsecond=0)
            start_date = end_date - timedelta(days=25)
            start_date = start_date.replace(hour=9, minute=15, second=0, microsecond=0)
            
            stocks_to_download = self.selected_stocks.copy()
            failed_stocks = []
            successful_downloads = 0
            
            # Initial download attempt
            for i, symbol in enumerate(stocks_to_download):
                try:
                    self.log_info(f"Processing {symbol} ({i+1}/{len(stocks_to_download)})")
                    instrument = self.instrument_map.get(symbol)
                    is_demo = instrument and instrument.get('demo', False)

                    if self.smartapi_client and not is_demo:
                        await self._retry_with_backoff(
                            self._download_real_data_with_validation,
                            symbol, start_date, end_date
                        )
                    else:
                        self.log_info(f"Using demo data for {symbol} (no SmartAPI client or demo token)")
                        await self._create_demo_data(symbol, start_date, end_date)

                    await self._generate_higher_timeframes(symbol)
                    successful_downloads += 1
                    self.log_info(f"[SUCCESS] Initial download for {symbol} successful.")
                except Exception as e:
                    failed_stocks.append(symbol)
                    self.log_error(f"[FAILED] Initial download for {symbol} failed after all retries: {e}")
                
                await asyncio.sleep(2.0) # Rate limiting

            # Global retry loop for failed stocks
            global_retry_attempts = 3
            for attempt in range(global_retry_attempts):
                if not failed_stocks:
                    break # Exit if no more stocks to retry

                self.log_info(f"--- Starting global retry attempt {attempt + 1}/{global_retry_attempts} for {len(failed_stocks)} failed stocks ---")
                stocks_to_retry_this_round = failed_stocks.copy()
                failed_stocks.clear() # Clear the list to re-populate with failures from this round

                for i, symbol in enumerate(stocks_to_retry_this_round):
                    try:
                        self.log_info(f"Retrying {symbol} ({i+1}/{len(stocks_to_retry_this_round)})")
                        await self._retry_with_backoff(
                            self._download_real_data_with_validation,
                            symbol, start_date, end_date
                        )
                        await self._generate_higher_timeframes(symbol)
                        successful_downloads += 1
                        self.log_info(f"[SUCCESS] Retry for {symbol} successful.")
                    except Exception as e:
                        failed_stocks.append(symbol) # Add back to the list if it fails again
                        self.log_error(f"[FAILED] Retry for {symbol} failed: {e}")
                    
                    await asyncio.sleep(2.0) # Rate limiting

            # Final summary statistics
            total_stocks = len(self.selected_stocks)
            final_failed_count = len(failed_stocks)
            final_success_count = total_stocks - final_failed_count
            success_rate = (final_success_count / total_stocks * 100) if total_stocks > 0 else 0
            
            self.log_info("--- Historical Data Download Summary ---")
            self.log_info(f"  [SUCCESS] Successful downloads: {final_success_count}/{total_stocks} ({success_rate:.1f}%)")
            self.log_info(f"  [FAILED] Failed downloads: {final_failed_count}/{total_stocks}")
            
            if final_failed_count > 0:
                self.log_warning(f"The following stocks could not be downloaded: {', '.join(failed_stocks)}")
                self.log_warning("System will continue with available data.")

        except Exception as e:
            self.log_error(f"An unexpected error occurred during the historical data download process: {e}")
    
    async def _download_historical_data(self):
        """Download historical data with proper date handling and a global retry mechanism for failed stocks."""
        try:
            self.log_info(f"Starting historical data download for {len(self.selected_stocks)} stocks...")
            
            end_date = datetime.now().replace(hour=15, minute=30, second=0, microsecond=0)
            start_date = end_date - timedelta(days=25)
            start_date = start_date.replace(hour=9, minute=15, second=0, microsecond=0)
            
            stocks_to_process = self.selected_stocks.copy()
            hard_failures = [] # Stocks that failed even after creating demo data
            successful_real_downloads = 0
            fallback_to_demo = 0

            # --- Initial Download Loop ---
            self.log_info(f"--- Starting initial download for {len(stocks_to_process)} stocks ---")
            for i, symbol in enumerate(stocks_to_process):
                try:
                    self.log_info(f"Processing {symbol} ({i+1}/{len(stocks_to_process)})")
                    instrument = self.instrument_map.get(symbol)
                    is_demo_token = instrument and instrument.get('demo', False)

                    if self.smartapi_client and not is_demo_token:
                        # This will try to download real data and will retry on failure.
                        await self._retry_with_backoff(
                            self._download_real_data_with_validation,
                            symbol, start_date, end_date
                        )
                        successful_real_downloads += 1
                        self.log_info(f"[SUCCESS] Real data downloaded for {symbol}.")
                    else:
                        # Use demo data if no client or if it's a designated demo token
                        self.log_info(f"Using demo data for {symbol} (no SmartAPI client or demo token).")
                        await self._create_demo_data(symbol, start_date, end_date)
                        fallback_to_demo += 1

                    # Generate higher timeframes for both real and demo data
                    await self._generate_higher_timeframes(symbol)

                except Exception as e:
                    # This block is reached if _retry_with_backoff fails completely for real data download
                    self.log_error(f"[FAILED] Real data download for {symbol} failed after all retries: {e}")
                    self.log_warning(f"Falling back to demo data for {symbol}.")
                    try:
                        await self._create_demo_data(symbol, start_date, end_date)
                        await self._generate_higher_timeframes(symbol)
                        fallback_to_demo += 1
                    except Exception as demo_e:
                        self.log_error(f"[CRITICAL] Could not create demo data for {symbol}: {demo_e}")
                        hard_failures.append(symbol) # Only critical failures are added for global retry

                await asyncio.sleep(1.5) # Rate limiting

            # --- Global Retry Loop for Hard Failures ---
            global_retry_attempts = 2
            for attempt in range(global_retry_attempts):
                if not hard_failures:
                    break # Exit if no more stocks to retry

                self.log_info(f"--- Starting global retry attempt {attempt + 1}/{global_retry_attempts} for {len(hard_failures)} critical failures ---")
                stocks_to_retry = hard_failures.copy()
                hard_failures.clear()

                for symbol in stocks_to_retry:
                    try:
                        self.log_info(f"Retrying critically failed stock: {symbol}")
                        # Re-run the full logic for this stock
                        await self._retry_with_backoff(self._download_real_data_with_validation, symbol, start_date, end_date)
                        await self._generate_higher_timeframes(symbol)
                        successful_real_downloads += 1
                        self.log_info(f"[SUCCESS] Global retry for {symbol} successful.")
                    except Exception:
                        self.log_error(f"[FAILED] Global retry for {symbol} failed again. Falling back to demo data.")
                        try:
                            await self._create_demo_data(symbol, start_date, end_date)
                            await self._generate_higher_timeframes(symbol)
                            fallback_to_demo += 1
                        except Exception as demo_e:
                            self.log_error(f"[CRITICAL] Demo data creation also failed for {symbol} on global retry: {demo_e}")
                            hard_failures.append(symbol) # Add back to the list if it fails again
                    
                    await asyncio.sleep(1.5)

            # --- Final Summary ---
            total_stocks = len(self.selected_stocks)
            final_failed_count = len(hard_failures)
            final_success_count = total_stocks - final_failed_count
            success_rate = (final_success_count / total_stocks * 100) if total_stocks > 0 else 0
            
            self.log_info("--- Historical Data Download Summary ---")
            self.log_info(f"  Total Stocks: {total_stocks}")
            self.log_info(f"  - Successful Real Data: {successful_real_downloads}")
            self.log_info(f"  - Fallback to Demo Data: {fallback_to_demo}")
            self.log_info(f"  - Hard Failures (No Data): {final_failed_count}")
            self.log_info(f"  Overall Success Rate (any data): {success_rate:.1f}%")
            
            if final_failed_count > 0:
                self.log_warning(f"The following stocks could not be downloaded or processed: {', '.join(hard_failures)}")

        except Exception as e:
            self.log_error(f"An unexpected error occurred during the historical data download process: {e}")

    async def download_historical_data_for_symbol(self, symbol: str, start_date: datetime, end_date: datetime) -> Optional[pl.DataFrame]:
        """
        Download historical data for any symbol by looking up token directly from token mapping.
        This method doesn't require the symbol to be in the instrument_map.
        Returns a Polars DataFrame or None if download fails.
        """
        # Initialize SmartAPI client if not already done
        if not self.smartapi_client:
            self.log_info("SmartAPI client not initialized, attempting to initialize...")
            credentials = self._load_credentials()
            if credentials:
                self.smartapi_client = ModernSmartAPIClient(credentials)
                auth_success = await self.smartapi_client.authenticate()
                if not auth_success:
                    self.log_error("SmartAPI authentication failed")
                    return None
                self.log_info("SmartAPI client initialized successfully")
            else:
                self.log_error(f"SmartAPI credentials not available for downloading data for {symbol}")
                return None
            
        # Look up token directly from token mapping
        keys_to_check = [
            f"{symbol}-EQ_NSE",
            f"{symbol}_NSE", 
            symbol
        ]
        
        token = None
        for key in keys_to_check:
            if key in self.symbol_to_token_map:
                token = self.symbol_to_token_map[key]
                break
                
        if not token:
            raise ValueError(f"No instrument found for {symbol}")
            
        try:
            # Get historical data in batches
            data = await self.smartapi_client.get_historical_data_batch(
                symbol_token=token,
                exchange='NSE',
                start_date=start_date,
                end_date=end_date,
                interval="ONE_MINUTE"
            )

            if not data:
                raise ValueError(f"API returned no data for {symbol}")

            # Convert to DataFrame with proper date parsing
            df_data = []
            parse_errors = 0
            for candle in data:
                try:
                    timestamp_str = candle[0]
                    timestamp = self._parse_smartapi_timestamp(timestamp_str)
                    df_data.append({
                        'timestamp': timestamp, 'open': float(candle[1]), 'high': float(candle[2]),
                        'low': float(candle[3]), 'close': float(candle[4]), 'volume': int(candle[5])
                    })
                except Exception as e:
                    parse_errors += 1
            
            if parse_errors > 0:
                self.log_warning(f"Failed to parse {parse_errors}/{len(data)} candles for {symbol}")

            if not df_data:
                raise ValueError(f"Failed to parse any valid candle data for {symbol}")

            # Validate data quality
            if len(df_data) < 10:
                raise ValueError(f"Insufficient real data for {symbol}: only {len(df_data)} candles")

            self.log_debug(f"Downloaded {len(df_data)} 1-min candles for {symbol}")
            return pl.DataFrame(df_data)
            
        except Exception as e:
            self.log_error(f"Failed to download historical data for {symbol}: {e}")
            raise

    async def _download_real_data_with_validation(self, symbol: str, start_date: datetime, end_date: datetime):
        """
        Download real data from SmartAPI. Raises an exception on failure to allow for retries.
        """
        instrument = self.instrument_map.get(symbol)
        if not instrument:
            raise ValueError(f"No instrument found for {symbol}")

        # Get historical data in batches
        data = await self.smartapi_client.get_historical_data_batch(
            symbol_token=instrument['token'],
            exchange=instrument['exchange'],
            start_date=start_date,
            end_date=end_date,
            interval="ONE_MINUTE"
        )

        if not data:
            # If API returns no data, it's a failure that should be retried.
            raise ValueError(f"API returned no data for {symbol}")

        # Convert to DataFrame with proper date parsing
        df_data = []
        parse_errors = 0
        for candle in data:
            try:
                timestamp_str = candle[0]
                timestamp = self._parse_smartapi_timestamp(timestamp_str)
                df_data.append({
                    'timestamp': timestamp, 'open': float(candle[1]), 'high': float(candle[2]),
                    'low': float(candle[3]), 'close': float(candle[4]), 'volume': int(candle[5])
                })
            except Exception as e:
                parse_errors += 1
        
        if parse_errors > 0:
            self.log_warning(f"Failed to parse {parse_errors}/{len(data)} candles for {symbol}")

        if not df_data:
            # If parsing fails for all candles, this is a retryable failure.
            raise ValueError(f"Failed to parse any valid candle data for {symbol}")

        # Validate data quality
        if len(df_data) < 10:
            # Insufficient data is also a retryable failure.
            raise ValueError(f"Insufficient real data for {symbol}: only {len(df_data)} candles")

        # Store the data
        self.timeframe_data["1min"][symbol] = pl.DataFrame(df_data)
        self.log_debug(f"Stored {len(df_data)} 1-min candles for {symbol}")
        return len(df_data)
    
    async def _download_real_data(self, symbol: str, start_date: datetime, end_date: datetime):
        """Download real data from SmartAPI (legacy method without retry)"""
        try:
            await self._download_real_data_with_validation(symbol, start_date, end_date)
        except Exception as e:
            self.log_error(f"Failed to download real data for {symbol}: {e}")
            raise
    
    def _parse_smartapi_timestamp(self, timestamp_str: str) -> datetime:
        """Parse SmartAPI timestamp with multiple format support"""
        try:
            # SmartAPI typically returns timestamps in these formats:
            formats = [
                "%Y-%m-%d %H:%M:%S",  # 2025-01-31 09:15:00
                "%d-%m-%Y %H:%M:%S",  # 31-01-2025 09:15:00 (sometimes)
                "%Y-%m-%dT%H:%M:%S",  # 2025-01-31T09:15:00
                "%Y-%m-%d %H:%M",     # 2025-01-31 09:15
            ]
            
            # Clean the timestamp string
            clean_timestamp = timestamp_str.strip()
            if '+' in clean_timestamp:
                clean_timestamp = clean_timestamp.split('+')[0]
            
            for fmt in formats:
                try:
                    return datetime.strptime(clean_timestamp, fmt)
                except ValueError:
                    continue
            
            # If all formats fail, try ISO format
            return datetime.fromisoformat(clean_timestamp.replace('T', ' '))
            
        except Exception as e:
            self.log_error(f"Failed to parse timestamp '{timestamp_str}': {e}")
            # Return current time as fallback
            return datetime.now()
    
    async def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime):
        """Create realistic demo data"""
        try:
            self.log_info(f"Creating demo data for {symbol}")
            
            # Generate realistic 1-minute data
            current_time = start_date
            demo_data = []
            base_price = random.uniform(100, 2000)  # Random base price
            
            while current_time < end_date:
                # Skip weekends
                if current_time.weekday() < 5:  # Monday = 0, Friday = 4
                    # Market hours: 9:15 AM to 3:30 PM
                    if (9 <= current_time.hour < 15) or (current_time.hour == 15 and current_time.minute <= 30):
                        if current_time.hour >= 9 and current_time.minute >= 15:
                            # Generate realistic price movement
                            price_change = random.uniform(-0.015, 0.015)  # ±1.5% change
                            base_price *= (1 + price_change)
                            
                            # Ensure positive price
                            base_price = max(base_price, 10)
                            
                            high = base_price * random.uniform(1.0, 1.008)
                            low = base_price * random.uniform(0.992, 1.0)
                            volume = random.randint(1000, 50000)
                            
                            demo_data.append({
                                'timestamp': current_time,
                                'open': round(base_price, 2),
                                'high': round(high, 2),
                                'low': round(low, 2),
                                'close': round(base_price, 2),
                                'volume': volume
                            })
                
                current_time += timedelta(minutes=1)
            
            # Store demo data
            if demo_data:
                self.timeframe_data["1min"][symbol] = pl.DataFrame(demo_data)
                self.log_info(f"Created {len(demo_data)} demo 1-min candles for {symbol}")
            
        except Exception as e:
            self.log_error(f"Failed to create demo data for {symbol}: {e}")
    
    async def _generate_higher_timeframes(self, symbol: str):
        """Generate higher timeframes from 1-minute data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            df_1min = self.timeframe_data["1min"][symbol]
            if len(df_1min) == 0:
                return
            
            # Training timeframe configurations
            timeframe_configs = {}
            for tf in self.timeframe_data.keys():
                if tf != "1min":  # Skip 1min as it's the base
                    if tf.endswith("min"):
                        minutes = int(tf.replace("min", ""))
                        timeframe_configs[tf] = f"{minutes}m"
                    elif tf.endswith("hr"):
                        hours = int(tf.replace("hr", ""))
                        timeframe_configs[tf] = f"{hours}h"
            
            for tf_name, tf_period in timeframe_configs.items():
                try:
                    # Use group_by_dynamic for time-based aggregation
                    df_tf = df_1min.group_by_dynamic(
                        "timestamp",
                        every=tf_period,
                        closed="left"
                    ).agg([
                        pl.col("open").first().alias("open"),
                        pl.col("high").max().alias("high"),
                        pl.col("low").min().alias("low"),
                        pl.col("close").last().alias("close"),
                        pl.col("volume").sum().alias("volume")
                    ]).filter(
                        pl.col("open").is_not_null()
                    )
                    
                    if len(df_tf) > 0:
                        self.timeframe_data[tf_name][symbol] = df_tf
                        self.log_debug(f"Generated {len(df_tf)} {tf_name} candles for {symbol}")
                    
                except Exception as e:
                    self.log_warning(f"Failed to generate {tf_name} for {symbol}: {e}")
            
        except Exception as e:
            self.log_error(f"Failed to generate higher timeframes for {symbol}: {e}")
    
    async def _initialize_websocket(self):
        """Initialize WebSocket connection"""
        try:
            self.log_info("Initializing WebSocket connection...")
            
            # Set up WebSocket callbacks
            callbacks = {
                'on_connect': self._on_websocket_connect,
                'on_data': self._on_websocket_data,
                'on_error': self._on_websocket_error,
                'on_close': self._on_websocket_close
            }
            
            # Initialize WebSocket
            success = await self.smartapi_client.initialize_websocket(callbacks)
            
            if success:
                # Subscribe to symbols
                await self._subscribe_to_symbols()
                self.log_info("WebSocket initialized and subscribed successfully")
            else:
                self.log_error("WebSocket initialization failed")
            
        except Exception as e:
            self.log_error(f"Failed to initialize WebSocket: {e}")
    
    async def _subscribe_to_symbols(self):
        """Subscribe to symbols for real-time data"""
        try:
            # Prepare subscription data
            token_list = []
            
            for symbol in self.selected_stocks:
                instrument = self.instrument_map.get(symbol)
                if instrument:
                    token_list.append({
                        "exchangeType": 1,  # NSE
                        "tokens": [instrument['token']]
                    })
            
            if token_list:
                # Subscribe to LTP mode
                success = self.smartapi_client.subscribe_symbols(token_list, mode=1)
                if success:
                    self.log_info(f"Subscribed to {len(token_list)} symbols")
                else:
                    self.log_error("Failed to subscribe to symbols")
            
        except Exception as e:
            self.log_error(f"Failed to subscribe to symbols: {e}")
    
    def _on_websocket_connect(self, ws):
        """WebSocket connect callback"""
        self.log_info("WebSocket connected")
        
        # Note: Cannot publish async events from sync callback
        # WebSocket connection status is tracked in smartapi_client
    
    def _on_websocket_data(self, ws, data):
        """WebSocket data callback"""
        try:
            # Process real-time data
            if isinstance(data, dict):
                symbol_token = data.get('tk')
                ltp = data.get('lp')
                
                if symbol_token and ltp:
                    # Find symbol by token
                    symbol = self._get_symbol_by_token(symbol_token)
                    if symbol:
                        # Store real-time data
                        self.realtime_data[symbol] = {
                            'symbol': symbol,
                            'ltp': float(ltp),
                            'timestamp': datetime.now(),
                            'volume': data.get('v', 0),
                            'open': data.get('o', ltp),
                            'high': data.get('h', ltp),
                            'low': data.get('l', ltp)
                        }
                        
                        # Note: Cannot publish async events from sync callback
                        # Real-time data is stored and can be accessed by other components
                        
                        self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"WebSocket data processing error: {e}")
    
    def _on_websocket_error(self, ws, error):
        """WebSocket error callback"""
        self.log_error(f"WebSocket error: {error}")
        
        # Note: Cannot publish async events from sync callback
        # Error is logged and tracked in smartapi_client
    
    def _on_websocket_close(self, ws, code, reason):
        """WebSocket close callback"""
        self.log_info(f"WebSocket closed: {code} - {reason}")
        
        # Note: Cannot publish async events from sync callback
        # Close status is tracked in smartapi_client
    
    def _get_symbol_by_token(self, token: str) -> Optional[str]:
        """Get symbol by token"""
        for symbol, instrument in self.instrument_map.items():
            if instrument['token'] == token:
                return symbol
        return None
    
    async def _handle_data_request(self, event):
        """Handle data request from other agents"""
        try:
            symbol = event.data.get('symbol')
            timeframe = event.data.get('timeframe', '1min')
            
            if symbol and symbol in self.timeframe_data.get(timeframe, {}):
                data = self.timeframe_data[timeframe][symbol]
                
                # Publish historical data loaded event
                await self.event_bus.publish(
                    EventTypes.HISTORICAL_DATA_LOADED,
                    {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'records': len(data) if data is not None else 0,
                        'data': data  # Pass the DataFrame directly
                    },
                    source=self.name
                )
                
                self.log_info(f"Provided data for {symbol} ({timeframe})")
            else:
                self.log_warning(f"No data available for {symbol} ({timeframe})")
                
        except Exception as e:
            self.log_error(f"Failed to handle data request: {e}")
    
    async def _start_data_loop(self):
        """Start the main data processing loop"""
        try:
            self.log_info("Starting data processing loop...")
            
            while self.running:
                try:
                    # Process any pending updates
                    await self._process_data_updates()
                    
                    # Health check
                    if self.smartapi_client and not self.smartapi_client.websocket_connected:
                        self.log_warning("WebSocket disconnected, attempting reconnection...")
                        # In a real implementation, add reconnection logic here
                    
                    # Sleep for a short interval
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.log_error(f"Error in data processing loop: {e}")
                    await asyncio.sleep(5)
            
            self.log_info("Data processing loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start data processing loop: {e}")
    
    async def _process_data_updates(self):
        """Process any pending data updates"""
        try:
            # Update 1-minute candles with real-time data
            for symbol, rt_data in self.realtime_data.items():
                await self._update_realtime_candle(symbol, rt_data)
            
        except Exception as e:
            self.log_error(f"Failed to process data updates: {e}")
    
    async def _update_realtime_candle(self, symbol: str, rt_data: Dict[str, Any]):
        """Update real-time 1-minute candle data"""
        try:
            if symbol not in self.timeframe_data["1min"]:
                return
            
            # Get current 1-minute candle timestamp
            current_minute = rt_data['timestamp'].replace(second=0, microsecond=0)
            
            # This is a simplified update - in production, you'd want more sophisticated logic
            # to properly update the current candle with real-time data
            
        except Exception as e:
            self.log_error(f"Failed to update realtime candle for {symbol}: {e}")
    
    async def get_historical_data(self, symbol: str, timeframe: str = "1min") -> Optional[pl.DataFrame]:
        """Get historical data for a symbol and timeframe"""
        try:
            if timeframe in self.timeframe_data and symbol in self.timeframe_data[timeframe]:
                return self.timeframe_data[timeframe][symbol]
            return None
            
        except Exception as e:
            self.log_error(f"Failed to get historical data for {symbol}: {e}")
            return None
    
    async def get_realtime_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time data for a symbol"""
        try:
            return self.realtime_data.get(symbol)
            
        except Exception as e:
            self.log_error(f"Failed to get realtime data for {symbol}: {e}")
            return None
    
    async def stop(self):
        """Stop the market data agent"""
        try:
            self.log_info("Stopping Clean Market Data Agent...")
            
            self.running = False
            
            # Disconnect WebSocket
            if self.smartapi_client:
                await self.smartapi_client.disconnect_websocket()
            
            self.log_info("Clean Market Data Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping agent: {e}")
    
    async def _perform_additional_health_checks(self) -> Dict[str, Any]:
        """Perform additional health checks"""
        try:
            checks = {}
            
            # Check data availability
            checks['data_available'] = len(self.timeframe_data["1min"]) > 0
            
            # Check SmartAPI client health
            if self.smartapi_client:
                checks['smartapi_healthy'] = self.smartapi_client.is_healthy()
                checks['websocket_connected'] = self.smartapi_client.websocket_connected
            else:
                checks['smartapi_healthy'] = False
                checks['websocket_connected'] = False
            
            # Check real-time data freshness
            if self.realtime_data:
                latest_data_time = max(data['timestamp'] for data in self.realtime_data.values())
                time_since_update = (datetime.now() - latest_data_time).total_seconds()
                checks['realtime_data_fresh'] = time_since_update < 300  # 5 minutes
            else:
                checks['realtime_data_fresh'] = False
            
            return checks
            
        except Exception as e:
            self.log_error(f"Health check failed: {e}")
            return {'error': str(e)}