#!/usr/bin/env python3
"""
MODERN SMARTAPI CLIENT
Clean SmartAPI integration with proper date handling and WebSocket v2

Features:
- Proper date format handling (YYYY-MM-DD HH:MM)
- Modern WebSocket v2 integration
- Async-friendly design
- Comprehensive error handling
- Rate limiting and retry logic
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
import time
import threading
from dataclasses import dataclass

try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    import pyotp
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class SmartAPICredentials:
    """SmartAPI credentials"""
    api_key: str
    username: str
    password: str
    totp_token: str

@dataclass
class HistoricalDataRequest:
    """Historical data request parameters"""
    symbol_token: str
    exchange: str
    interval: str
    from_date: datetime
    to_date: datetime

class ModernSmartAPIClient:
    """
    Modern SmartAPI client with proper async handling
    
    Features:
    - Correct date format handling
    - Rate limiting
    - Automatic retry logic
    - WebSocket v2 integration
    - Comprehensive error handling
    """
    
    def __init__(self, credentials: SmartAPICredentials):
        self.credentials = credentials
        self.client = None
        self.auth_token = None
        self.refresh_token = None
        self.feed_token = None
        
        # WebSocket
        self.websocket_client = None
        self.websocket_connected = False
        self.websocket_callbacks = {}
        
        # Rate limiting - more conservative to avoid AB1004 errors
        self.last_api_call = 0
        self.min_api_interval = 2.0  # 2 seconds between API calls
        
        # Statistics
        self.stats = {
            'api_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'websocket_messages': 0,
            'last_activity': None
        }
        
        logger.info("[INIT] Modern SmartAPI client initialized")
    
    async def authenticate(self) -> bool:
        """Authenticate with SmartAPI"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("[ERROR] SmartAPI not available. Install with: pip install smartapi-python")
                return False
            
            logger.info("[AUTH] Authenticating with SmartAPI...")
            
            # Initialize SmartConnect
            self.client = SmartConnect(api_key=self.credentials.api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(self.credentials.totp_token)
            totp_code = totp.now()
            
            # Login
            data = self.client.generateSession(
                clientCode=self.credentials.username,
                password=self.credentials.password,
                totp=totp_code
            )
            
            if data and data.get('status'):
                self.auth_token = data['data']['jwtToken']
                self.refresh_token = data['data']['refreshToken']
                self.feed_token = self.client.getfeedToken()
                
                logger.info("[SUCCESS] SmartAPI authentication successful")
                return True
            else:
                error_msg = data.get('message', 'Unknown error') if data else 'No response'
                logger.error(f"[ERROR] SmartAPI authentication failed: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Authentication failed: {e}")
            return False
    
    async def get_historical_data(self, request: HistoricalDataRequest) -> Optional[List[Dict[str, Any]]]:
        """
        Get historical data with proper date format and timeout handling
        
        SmartAPI expects dates in format: YYYY-MM-DD HH:MM
        """
        try:
            if not self.client:
                logger.error("[ERROR] Client not authenticated")
                return None
            
            # Apply rate limiting
            await self._apply_rate_limit()
            
            # Format dates correctly for SmartAPI
            from_date = request.from_date.strftime("%Y-%m-%d %H:%M")
            to_date = request.to_date.strftime("%Y-%m-%d %H:%M")
            
            logger.debug(f"[API] Requesting historical data: {request.symbol_token} from {from_date} to {to_date}")
            
            # Make API call with timeout in executor to avoid blocking
            loop = asyncio.get_event_loop()
            
            def make_api_call():
                return self.client.getCandleData({
                    "exchange": request.exchange,
                    "symboltoken": request.symbol_token,
                    "interval": request.interval,
                    "fromdate": from_date,
                    "todate": to_date
                })
            
            # Execute with timeout (30 seconds)
            try:
                response = await asyncio.wait_for(
                    loop.run_in_executor(None, make_api_call),
                    timeout=30.0
                )
            except asyncio.TimeoutError:
                logger.error(f"[TIMEOUT] Historical data request timed out for {request.symbol_token}")
                self.stats['failed_calls'] += 1
                return None
            
            self.stats['api_calls'] += 1
            self.stats['last_activity'] = datetime.now()
            
            if response and response.get('status'):
                data = response.get('data', [])
                self.stats['successful_calls'] += 1

                logger.debug(f"[SUCCESS] Retrieved {len(data)} candles for {request.symbol_token}")
                return data
            else:
                error_msg = response.get('message', 'Unknown error') if response else 'No response'
                error_code = response.get('errorcode', 'Unknown') if response else 'No response'

                # Handle specific error codes
                if 'AB1004' in str(error_code) or 'invalid token' in str(error_msg).lower():
                    logger.error(f"[INVALID_TOKEN] Invalid token {request.symbol_token}: {error_msg}")
                elif 'AB1010' in str(error_code):
                    logger.error(f"[RATE_LIMIT] Rate limit exceeded for {request.symbol_token}: {error_msg}")
                else:
                    logger.error(f"[API_ERROR] Failed to get data for {request.symbol_token}: {error_msg} (Code: {error_code})")
                logger.error(f"[ERROR] Historical data request failed: {error_msg}")
                self.stats['failed_calls'] += 1
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to get historical data: {e}")
            self.stats['failed_calls'] += 1
            return None
    
    async def get_historical_data_batch(self, symbol_token: str, exchange: str, 
                                       start_date: datetime, end_date: datetime,
                                       interval: str = "ONE_MINUTE") -> List[Dict[str, Any]]:
        """
        Get historical data in batches to handle large date ranges
        
        SmartAPI has limits on date range, so we split into smaller batches
        """
        try:
            all_data = []
            current_start = start_date
            batch_size_days = 30  # 30-day batches
            
            while current_start < end_date:
                batch_end = min(current_start + timedelta(days=batch_size_days), end_date)
                
                request = HistoricalDataRequest(
                    symbol_token=symbol_token,
                    exchange=exchange,
                    interval=interval,
                    from_date=current_start,
                    to_date=batch_end
                )
                
                batch_data = await self.get_historical_data(request)
                if batch_data:
                    all_data.extend(batch_data)
                    logger.debug(f"[BATCH] Retrieved {len(batch_data)} candles for batch {current_start.date()} to {batch_end.date()}")
                else:
                    logger.warning(f"[WARN] Failed to get data for batch {current_start.date()} to {batch_end.date()}")
                
                current_start = batch_end
                
                # Rate limiting between batches - increased to avoid timeouts
                await asyncio.sleep(3.0)  # Increased to 3 seconds
            
            logger.info(f"[SUCCESS] Retrieved total {len(all_data)} candles for {symbol_token}")
            return all_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get historical data batch: {e}")
            return []
    
    async def initialize_websocket(self, callbacks: Dict[str, Callable]) -> bool:
        """Initialize WebSocket v2 connection"""
        try:
            if not self.auth_token or not self.feed_token:
                logger.error("[ERROR] Authentication tokens not available for WebSocket")
                return False
            
            logger.info("[WEBSOCKET] Initializing WebSocket v2 connection...")
            
            # Store callbacks
            self.websocket_callbacks = callbacks
            
            # Create WebSocket client
            self.websocket_client = SmartWebSocketV2(
                auth_token=self.auth_token,
                api_key=self.credentials.api_key,
                client_code=self.credentials.username,
                feed_token=self.feed_token
            )
            
            # Set up callbacks
            self.websocket_client.on_open = self._on_websocket_open
            self.websocket_client.on_data = self._on_websocket_data
            self.websocket_client.on_error = self._on_websocket_error
            self.websocket_client.on_close = self._on_websocket_close
            
            # Connect in separate thread (SmartAPI WebSocket is not async)
            success = await self._connect_websocket_async()
            
            if success:
                logger.info("[SUCCESS] WebSocket connected successfully")
                return True
            else:
                logger.error("[ERROR] WebSocket connection failed")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize WebSocket: {e}")
            return False
    
    async def _connect_websocket_async(self) -> bool:
        """Connect WebSocket in async-friendly way"""
        try:
            # Create connection event
            connection_event = asyncio.Event()
            connection_result = {'success': False}
            
            def connect_worker():
                try:
                    self.websocket_client.connect()
                    connection_result['success'] = True
                except Exception as e:
                    logger.error(f"[ERROR] WebSocket connection error: {e}")
                    connection_result['success'] = False
                finally:
                    connection_event.set()
            
            # Start connection thread
            connection_thread = threading.Thread(target=connect_worker, daemon=True)
            connection_thread.start()
            
            # Wait for connection with timeout
            try:
                await asyncio.wait_for(connection_event.wait(), timeout=10.0)
                return connection_result['success'] and self.websocket_connected
            except asyncio.TimeoutError:
                logger.error("[ERROR] WebSocket connection timeout")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to connect WebSocket async: {e}")
            return False
    
    def subscribe_symbols(self, symbols: List[Dict[str, Any]], mode: int = 1) -> bool:
        """Subscribe to symbols for real-time data"""
        try:
            if not self.websocket_connected or not self.websocket_client:
                logger.error("[ERROR] WebSocket not connected")
                return False
            
            # Create correlation ID
            correlation_id = f"sub_{int(time.time())}"
            
            # Subscribe to symbols
            self.websocket_client.subscribe(correlation_id, mode, symbols)
            
            logger.info(f"[WEBSOCKET] Subscribed to {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to symbols: {e}")
            return False
    
    def unsubscribe_symbols(self, symbols: List[Dict[str, Any]], mode: int = 1) -> bool:
        """Unsubscribe from symbols"""
        try:
            if not self.websocket_connected or not self.websocket_client:
                logger.error("[ERROR] WebSocket not connected")
                return False
            
            # Create correlation ID
            correlation_id = f"unsub_{int(time.time())}"
            
            # Unsubscribe from symbols
            self.websocket_client.unsubscribe(correlation_id, mode, symbols)
            
            logger.info(f"[WEBSOCKET] Unsubscribed from {len(symbols)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to unsubscribe from symbols: {e}")
            return False
    
    async def disconnect_websocket(self):
        """Disconnect WebSocket"""
        try:
            if self.websocket_client:
                self.websocket_client.close_connection()
                self.websocket_connected = False
                logger.info("[WEBSOCKET] Disconnected")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to disconnect WebSocket: {e}")
    
    def _on_websocket_open(self, ws):
        """WebSocket open callback"""
        try:
            logger.info("[WEBSOCKET] Connection opened")
            self.websocket_connected = True
            
            if 'on_connect' in self.websocket_callbacks:
                try:
                    self.websocket_callbacks['on_connect'](ws)
                except Exception as callback_error:
                    logger.error(f"[ERROR] Error in WebSocket connect callback: {callback_error}")
                
        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket open callback: {e}")
    
    def _on_websocket_data(self, ws, data):
        """WebSocket data callback"""
        try:
            self.stats['websocket_messages'] += 1
            self.stats['last_activity'] = datetime.now()
            
            if 'on_data' in self.websocket_callbacks:
                self.websocket_callbacks['on_data'](ws, data)
                
        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket data callback: {e}")
    
    def _on_websocket_error(self, ws, *args):
        """WebSocket error callback - flexible argument handling"""
        try:
            # Handle different callback signatures
            error = args[0] if len(args) > 0 else "Unknown error"
            
            logger.error(f"[WEBSOCKET] Error: {error}")
            self.websocket_connected = False
            
            if 'on_error' in self.websocket_callbacks:
                try:
                    self.websocket_callbacks['on_error'](ws, error)
                except TypeError:
                    # Fallback for callbacks expecting fewer arguments
                    self.websocket_callbacks['on_error'](ws)
                
        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket error callback: {e}")
    
    def _on_websocket_close(self, ws, *args):
        """WebSocket close callback - flexible argument handling"""
        try:
            # Handle different callback signatures
            code = args[0] if len(args) > 0 else None
            reason = args[1] if len(args) > 1 else None
            
            logger.info(f"[WEBSOCKET] Connection closed: {code} - {reason}")
            self.websocket_connected = False
            
            if 'on_close' in self.websocket_callbacks:
                # Call with flexible arguments
                try:
                    self.websocket_callbacks['on_close'](ws, code, reason)
                except TypeError:
                    # Fallback for callbacks expecting fewer arguments
                    self.websocket_callbacks['on_close'](ws)
                
        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket close callback: {e}")
    
    async def _apply_rate_limit(self):
        """Apply rate limiting to API calls"""
        try:
            current_time = time.time()
            time_since_last_call = current_time - self.last_api_call
            
            if time_since_last_call < self.min_api_interval:
                sleep_time = self.min_api_interval - time_since_last_call
                await asyncio.sleep(sleep_time)
            
            self.last_api_call = time.time()
            
        except Exception as e:
            logger.error(f"[ERROR] Rate limiting failed: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get client statistics"""
        return {
            'authenticated': self.auth_token is not None,
            'websocket_connected': self.websocket_connected,
            'api_calls': self.stats['api_calls'],
            'successful_calls': self.stats['successful_calls'],
            'failed_calls': self.stats['failed_calls'],
            'websocket_messages': self.stats['websocket_messages'],
            'success_rate': self.stats['successful_calls'] / max(self.stats['api_calls'], 1),
            'last_activity': self.stats['last_activity'].isoformat() if self.stats['last_activity'] else None
        }
    
    def is_healthy(self) -> bool:
        """Check if client is healthy"""
        if not self.client or not self.auth_token:
            return False
        
        # Check success rate
        if self.stats['api_calls'] > 10:
            success_rate = self.stats['successful_calls'] / self.stats['api_calls']
            if success_rate < 0.8:  # Less than 80% success rate
                return False
        
        return True