#!/usr/bin/env python3
"""
CLEAN TRADING SYSTEM RUNNER
Modern implementation with a pre-run analysis workflow for stock selection.

Features:
- Pre-run analysis of all F&O stocks to select the best universe.
- Data-driven selection based on calculated features and forward returns.
- Concurrent data fetching and processing for speed.
- Dynamic universe is then fed into live trading agents.
"""

import asyncio
import logging
import argparse
import signal
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import sys
import pandas as pd
import polars as pl
import numpy as np
from dotenv import load_dotenv
import time # Added for sleep
import pyarrow.parquet as pq # For parquet operations
import pyarrow as pa # For parquet operations
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
load_dotenv(Path(__file__).parent.parent / '.env')

from core.event_system import EventBus, EventTypes
from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent
from agents.modern_execution_agent import ModernExecutionAgent
from agents.live_stock_selection_workflow import LiveStockSelectionWorkflow

logger = logging.getLogger(__name__)

class MLUniversePreparer:
    """
    ML-driven universe preparation using the Live Stock Selection Workflow.
    Replaces the simple feature-based approach with comprehensive ML analysis.
    """
    def __init__(self, event_bus: EventBus, market_data_agent: CleanMarketDataAgent):
        self.event_bus = event_bus
        self.market_data_agent = market_data_agent
        self.ml_workflow: Optional[LiveStockSelectionWorkflow] = None
        self.project_root = Path(__file__).parent.parent
        self.live_data_path = self.project_root / "data" / "live"
        self.feature_data_path = self.project_root / "data" / "features" # Corrected path as per user feedback

    def _download_single_stock_sync(self, symbol: str, start_date: datetime, end_date: datetime, stock_index: int, total_stocks: int) -> Tuple[str, bool]:
        """
        Download historical data for a single stock synchronously (for use with ThreadPoolExecutor).
        Returns tuple of (symbol, success_status).
        """
        logger.info(f"[ML-PREP] Downloading 1-min data for {symbol} ({stock_index}/{total_stocks}) from SmartAPI...")
        try:
            # Create a new event loop for this thread since we need to call async method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Use the new method that can download data for any symbol
                df_1min_polars = loop.run_until_complete(
                    self.market_data_agent.download_historical_data_for_symbol(
                        symbol=symbol,
                        start_date=start_date,
                        end_date=end_date
                    )
                )

                if df_1min_polars is not None and not df_1min_polars.is_empty():
                    # Convert Polars DataFrame to Pandas DataFrame for consistency with existing code
                    df_1min_pd = df_1min_polars.to_pandas()
                    
                    # Ensure 'timestamp' column is datetime and set as index
                    if 'timestamp' in df_1min_pd.columns:
                        df_1min_pd['timestamp'] = pd.to_datetime(df_1min_pd['timestamp'])
                        df_1min_pd = df_1min_pd.set_index('timestamp').sort_index()
                    else:
                        logger.error(f"[ML-PREP] Downloaded data for {symbol} does not contain 'timestamp' column.")
                        return symbol, False

                    # Save 1-min data to data/live
                    output_file_1min = self.live_data_path / f"{symbol}_1min.parquet"
                    df_1min_pd.to_parquet(output_file_1min, compression='brotli', engine='pyarrow')
                    logger.info(f"[ML-PREP] Saved 1-min data for {symbol} to {output_file_1min}")
                    return symbol, True
                else:
                    logger.warning(f"[ML-PREP] No 1-min data downloaded for {symbol} from SmartAPI.")
                    return symbol, False
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"[ML-PREP] Error downloading/saving data for {symbol} from SmartAPI: {e}")
            return symbol, False

    async def _download_historical_data(self, stocks: List[str]):
        """
        Downloads 25 days of 1-minute historical data for the given stocks from SmartAPI
        using ThreadPoolExecutor with optimized settings and retry mechanism.
        """
        self.live_data_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"[ML-PREP] Starting optimized historical data download from SmartAPI for {len(stocks)} stocks...")

        end_date = datetime.now()
        start_date = end_date - timedelta(days=25) # 25 days historical data

        # Configuration optimized based on reference file
        MAX_CONCURRENT_DOWNLOADS = 3  # Maximum concurrent downloads for SmartAPI
        RATE_LIMIT_DELAY = 0.5  # Minimum delay between requests (seconds)
        MAX_RETRIES = 3  # Maximum retry attempts for failed downloads
        RETRY_DELAY_BASE = 1.0  # Base delay for exponential backoff
        
        logger.info("🚀 STARTING OPTIMIZED DOWNLOAD")
        logger.info("="*60)
        logger.info(f"📊 Symbols: {len(stocks)}")
        logger.info(f"📅 Period: 25 days ({start_date} to {end_date})")
        logger.info(f"🧵 Max concurrent: {MAX_CONCURRENT_DOWNLOADS}")
        logger.info(f"⏳ Rate limit delay: {RATE_LIMIT_DELAY}s")
        logger.info(f"🔄 Max retries: {MAX_RETRIES}")
        logger.info("="*60)

        # Track statistics
        successful_downloads = 0
        failed_downloads = 0
        failed_stocks = []
        
        # Run the download process in a thread pool to avoid blocking the event loop
        def run_threaded_downloads():
            results = {}
            
            # Use ThreadPoolExecutor for concurrent downloads
            with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_DOWNLOADS) as executor:
                future_to_symbol = {
                    executor.submit(
                        self._download_single_stock_sync, 
                        symbol, 
                        start_date, 
                        end_date, 
                        i + 1, 
                        len(stocks)
                    ): symbol
                    for i, symbol in enumerate(stocks)
                }

                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        symbol_result, success = future.result()
                        results[symbol] = success
                        if not success:
                            failed_stocks.append(symbol)
                        
                        # Add rate limiting delay
                        time.sleep(RATE_LIMIT_DELAY)
                        
                    except Exception as e:
                        logger.error(f"[ML-PREP] Error downloading {symbol}: {e}")
                        results[symbol] = False
                        failed_stocks.append(symbol)
            
            return results

        # Execute downloads in thread pool
        start_time = time.time()
        results = await asyncio.get_event_loop().run_in_executor(None, run_threaded_downloads)
        
        # Count initial results
        successful_downloads = sum(1 for success in results.values() if success)
        failed_downloads = len(results) - successful_downloads
        
        logger.info(f"[ML-PREP] Initial download completed - Success: {successful_downloads}, Failed: {failed_downloads}")
        
        # Retry failed downloads with exponential backoff
        if failed_stocks and MAX_RETRIES > 0:
            logger.info(f"[ML-PREP] Retrying {len(failed_stocks)} failed downloads...")
            
            for retry_attempt in range(1, MAX_RETRIES + 1):
                if not failed_stocks:
                    break
                    
                retry_delay = RETRY_DELAY_BASE * (2 ** (retry_attempt - 1))  # Exponential backoff
                logger.info(f"[ML-PREP] Retry attempt {retry_attempt}/{MAX_RETRIES} for {len(failed_stocks)} stocks (delay: {retry_delay}s)")
                
                await asyncio.sleep(retry_delay)
                
                # Retry failed stocks
                def retry_downloads():
                    retry_results = {}
                    with ThreadPoolExecutor(max_workers=min(MAX_CONCURRENT_DOWNLOADS, len(failed_stocks))) as executor:
                        future_to_symbol = {
                            executor.submit(
                                self._download_single_stock_sync, 
                                symbol, 
                                start_date, 
                                end_date, 
                                stocks.index(symbol) + 1, 
                                len(stocks)
                            ): symbol
                            for symbol in failed_stocks
                        }

                        for future in as_completed(future_to_symbol):
                            symbol = future_to_symbol[future]
                            try:
                                symbol_result, success = future.result()
                                retry_results[symbol] = success
                                time.sleep(RATE_LIMIT_DELAY)
                            except Exception as e:
                                logger.error(f"[ML-PREP] Retry error for {symbol}: {e}")
                                retry_results[symbol] = False
                    
                    return retry_results
                
                retry_results = await asyncio.get_event_loop().run_in_executor(None, retry_downloads)
                
                # Update results and failed list
                retry_successful = 0
                new_failed_stocks = []
                for symbol, success in retry_results.items():
                    if success:
                        retry_successful += 1
                        successful_downloads += 1
                        failed_downloads -= 1
                    else:
                        new_failed_stocks.append(symbol)
                
                failed_stocks = new_failed_stocks
                logger.info(f"[ML-PREP] Retry {retry_attempt} completed - Additional successes: {retry_successful}, Still failed: {len(failed_stocks)}")

        # Calculate performance metrics
        end_time = time.time()
        total_time = end_time - start_time
        symbols_per_minute = (len(stocks) / total_time) * 60 if total_time > 0 else 0
        success_rate = (successful_downloads / len(stocks)) * 100 if len(stocks) > 0 else 0

        # Final summary
        logger.info("="*80)
        logger.info("🎯 OPTIMIZED DOWNLOAD SUMMARY")
        logger.info("="*80)
        logger.info(f"📊 Total symbols: {len(stocks)}")
        logger.info(f"✅ Successful downloads: {successful_downloads}")
        logger.info(f"❌ Failed downloads: {failed_downloads}")
        logger.info(f"📈 Success rate: {success_rate:.1f}%")
        logger.info(f"⏱️  Total time: {total_time:.1f} seconds")
        logger.info(f"🚀 Speed: {symbols_per_minute:.1f} symbols/minute")
        logger.info(f"⚡ Avg time per symbol: {total_time/len(stocks):.2f} seconds")
        if failed_stocks:
            logger.warning(f"🔴 Final failed stocks: {failed_stocks}")
        logger.info("="*80)

    async def _perform_feature_engineering(self, stocks: List[str]):
        """
        Performs feature engineering on the downloaded and aggregated data in data/live.
        """
        logger.info(f"[ML-PREP] Starting feature engineering for {len(stocks)} stocks...")
        
        for i, symbol in enumerate(stocks):
            # Perform feature engineering on the 1-minute data in data/live
            input_file = self.live_data_path / f"{symbol}_1min.parquet"
            if input_file.exists():
                try:
                    df = pd.read_parquet(input_file, engine='pyarrow')
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.set_index('timestamp').sort_index()

                    # Example feature engineering: Moving Averages and RSI
                    df['MA_5'] = df['close'].rolling(window=5).mean()
                    df['MA_10'] = df['close'].rolling(window=10).mean()
                    
                    # Placeholder for RSI calculation (requires more data/logic for proper calculation)
                    # For a real scenario, you'd use a library like `ta` or implement it fully.
                    delta = df['close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                    rs = gain / loss
                    df['RSI'] = 100 - (100 / (1 + rs))

                    # Save processed data back to data/live (overwriting or to a new 'features_live' folder)
                    # For now, let's overwrite the 1-min file with features.
                    df.reset_index().to_parquet(input_file, compression='brotli', engine='pyarrow')
                    logger.info(f"[ML-PREP] Performed feature engineering for {symbol} ({i+1}/{len(stocks)}) on 1-min data.")
                except Exception as e:
                    logger.error(f"[ML-PREP] Error during feature engineering for {symbol}: {e}")
            else:
                logger.warning(f"[ML-PREP] 1-min data file not found for {symbol} at {input_file}. Skipping feature engineering.")
            await asyncio.sleep(0.05) # Small sleep to avoid overwhelming

        logger.info("[ML-PREP] Feature engineering complete.")

    async def run(self) -> List[str]:
        """Execute the ML-driven universe preparation workflow."""
        logger.info("[ML-PREP] Starting ML-driven universe preparation workflow...")

        try:
            # Initialize the ML workflow
            self.ml_workflow = LiveStockSelectionWorkflow(self.event_bus, self.market_data_agent.session_id)

            # Initialize all ML agents
            success = await self.ml_workflow.initialize()
            if not success:
                logger.error("[ML-PREP] ML workflow initialization failed. Cannot proceed without a valid workflow.")
                return [] # Return empty list to indicate failure

            # Load stocks to process from the data/features folder
            stocks_to_process = set() # Use a set to store unique symbols
            if self.feature_data_path.exists():
                for file_path in self.feature_data_path.glob("features_*.parquet"):
                    # Extract symbol from filename, e.g., "features_VEDL_5min.parquet" -> "VEDL"
                    parts = file_path.stem.split('_')
                    if len(parts) >= 2:
                        stocks_to_process.add(parts[1])
                logger.info(f"[ML-PREP] Loaded {len(stocks_to_process)} unique stocks from {self.feature_data_path} for processing.")
            else:
                logger.error(f"[ML-PREP] Feature data path {self.feature_data_path} not found. Cannot proceed without stock data.")
                return [] # Return empty list to indicate failure

            if not stocks_to_process:
                logger.error("[ML-PREP] No stocks found in feature data path. Cannot proceed with an empty stock list.")
                return [] # Return empty list to indicate failure
            
            stocks_to_process_list = list(stocks_to_process) # Convert set to list for consistent processing

            # 1) Download historical data
            await self._download_historical_data(stocks_to_process_list)

            # 2) Do feature engineering on downloaded data
            await self._perform_feature_engineering(stocks_to_process_list)

            # 3) Load trained models from the existing workflow (handled by LiveStockSelectionWorkflow.execute_workflow)
            # 4) Stock Selection Agent (handled by LiveStockSelectionWorkflow.execute_workflow)
            logger.info("[ML-PREP] Executing ML-driven stock selection...")
            workflow_result = await self.ml_workflow.execute_workflow(stocks_to_process_list)

            if workflow_result and workflow_result.selected_stocks:
                selected_stocks = workflow_result.selected_stocks
                logger.info(f"[ML-PREP] ML workflow selected {len(selected_stocks)} stocks")

                # Log summary information
                self._log_ml_results(workflow_result)

                # Cleanup ML workflow resources
                await self.ml_workflow.cleanup()

                return selected_stocks
            else:
                logger.error("[ML-PREP] ML workflow returned no stocks. Cannot proceed with an empty selected stock list.")
                return [] # Return empty list to indicate failure

        except Exception as e:
            logger.error(f"[ML-PREP] ML workflow failed: {e}. Shutting down.")
            return []

    def _log_ml_results(self, workflow_result):
        """Log ML workflow results summary"""
        try:
            logger.info(f"[ML-PREP] === ML WORKFLOW RESULTS SUMMARY ===")
            logger.info(f"[ML-PREP] Selected stocks: {len(workflow_result.selected_stocks)}")
            logger.info(f"[ML-PREP] Execution time: {workflow_result.execution_summary.get('total_execution_time', 0):.1f}s")

            # Log strategy distribution
            if workflow_result.strategy_assignments:
                strategy_counts = {}
                for assignment in workflow_result.strategy_assignments.values():
                    strategy = assignment.get('primary_strategy', 'unknown')
                    strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

                logger.info(f"[ML-PREP] Strategy distribution:")
                for strategy, count in strategy_counts.items():
                    logger.info(f"[ML-PREP]   {strategy}: {count} stocks")

            # Log top selected stocks
            top_stocks = workflow_result.selected_stocks[:10]
            logger.info(f"[ML-PREP] Top 10 selected stocks: {top_stocks}")

            # Log warnings if any
            if workflow_result.warnings:
                logger.info(f"[ML-PREP] Warnings ({len(workflow_result.warnings)}):")
                for warning in workflow_result.warnings[:5]:  # Log first 5 warnings
                    logger.info(f"[ML-PREP]   • {warning}")

            # Log recommendations
            if workflow_result.recommendations:
                logger.info(f"[ML-PREP] Recommendations ({len(workflow_result.recommendations)}):")
                for rec in workflow_result.recommendations[:3]:  # Log first 3 recommendations
                    logger.info(f"[ML-PREP]   • {rec}")

        except Exception as e:
            logger.error(f"[ML-PREP] Error logging ML results: {e}")

class CleanTradingSystem:
    """Orchestrates the entire trading system from pre-run analysis to live trading."""
    
    def __init__(self, mode: str = "paper"):
        self.mode = mode
        self.running = False
        self.session_id = f"clean_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.event_bus = EventBus()
        self.config = self._create_config(mode)
        self.agents: Dict[str, Any] = {}

    def _create_config(self, mode: str) -> Any:
        """Creates a simple config object."""
        class Config:
            def __init__(self, mode):
                self.mode = mode
                self.selected_stocks = []
                self.timeframes = ["1min", "5min", "15min"]
                self.initial_balance = 1000000 # Default initial balance for paper trading
        return Config(mode)

    async def start(self):
        """Main startup sequence for the trading system."""
        logger.info(f"[START] Starting Clean Trading System in {self.mode} mode...")
        await self.event_bus.start_processor()

        # 1. Initialize Market Data Agent. Its startup will be called after stock selection.
        self.agents['market_data'] = CleanMarketDataAgent(self.event_bus, self.config, self.session_id)

        # 2. Run the ML-driven universe preparation workflow.
        preparer = MLUniversePreparer(self.event_bus, self.agents['market_data'])
        self.config.selected_stocks = await preparer.run()

        if not self.config.selected_stocks:
            logger.error("[START] Universe selection failed. Shutting down.")
            await self.stop()
            return

        # 3. Now that stocks are selected, start the Market Data Agent.
        # This ensures instruments are initialized with the selected universe.
        await self.agents['market_data'].startup()

        # 4. Initialize and start the remaining agents.
        self.agents['signal_generation'] = CleanSignalAgent(self.event_bus, self.config, self.session_id)
        self.agents['execution'] = ModernExecutionAgent(self.event_bus, self.config, self.session_id)
        
        # Start agents. Note: ModernExecutionAgent uses 'start' not 'startup'
        if not await self.agents['signal_generation'].startup():
            raise Exception(f"Failed to start agent: signal_generation")
        if not await self.agents['execution'].start(): # Changed to .start()
            raise Exception(f"Failed to start agent: execution")

        # 5. Broadcast the final universe to all agents for their own setup (e.g., websocket subscription).
        # The Market Data Agent should now be able to use this universe for its subscriptions.
        await self.event_bus.publish("BROADCAST_UNIVERSE", {'universe': self.config.selected_stocks}, source="TradingSystem")

        self.running = True
        logger.info("[SUCCESS] All agents started. System is now in live trading mode.")
        
        # Main loop for health checks
        while self.running:
            await asyncio.sleep(60) # Health check every minute
            await self._check_agent_health()

    async def _check_agent_health(self):
        health_statuses = await asyncio.gather(*(agent.health_check() for agent in self.agents.values()))
        for status in health_statuses:
            if not status['healthy']:
                logger.warning(f"[HEALTH] Agent {status['agent_name']} is unhealthy. Status: {status}")

    async def stop(self):
        logger.info("[STOP] Stopping Clean Trading System...")
        self.running = False
        await asyncio.gather(*(agent.shutdown() for agent in self.agents.values()), return_exceptions=True)
        await self.event_bus.stop_processor()
        logger.info("[SUCCESS] Clean Trading System stopped.")

    def _setup_signal_handlers(self):
        # Signal handlers are not supported on Windows for asyncio
        if sys.platform != "win32":
            loop = asyncio.get_running_loop()
            for sig in (signal.SIGINT, signal.SIGTERM):
                loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self._signal_handler(s)))
        else:
            logger.warning("Signal handlers are not supported on Windows. Use Ctrl+C to stop the system.")

    async def _signal_handler(self, sig):
        logger.info(f"[SIGNAL] Received signal {sig.name}, initiating shutdown...")
        await self.stop()

async def main():
    parser = argparse.ArgumentParser(description='Clean Trading System')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    args = parser.parse_args()

    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    logging.basicConfig(
        level=args.log_level.upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(), logging.FileHandler(log_dir / 'clean_trading_system.log')]
    )

    trading_system = CleanTradingSystem(args.mode)
    trading_system._setup_signal_handlers()
    
    try:
        await trading_system.start()
    except Exception as e:
        logger.error(f"[FATAL] System error: {e}", exc_info=True)
    finally:
        if trading_system.running:
            await trading_system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] System shutdown complete.")
